%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9017800197243233644
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 42ef2681fa3dc8c4fa031f044e68c63f, type: 3}
  m_Name: GlobalIllumination
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 0
    m_Value: 1
  enable:
    m_OverrideState: 1
    m_Value: 1
  tracing:
    m_OverrideState: 1
    m_Value: 4
  rayMiss:
    m_OverrideState: 1
    m_Value: 2
  adaptiveProbeVolumesLayerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 0
      m_Bits: 1
  depthBufferThickness:
    m_OverrideState: 0
    m_Value: 0.1
  fullResolutionSS:
    m_OverrideState: 0
    m_Value: 1
  m_MaxRaySteps:
    m_OverrideState: 0
    m_Value: 64
  m_DenoiseSS:
    m_OverrideState: 0
    m_Value: 1
  m_HalfResolutionDenoiserSS:
    m_OverrideState: 0
    m_Value: 0
  m_DenoiserRadiusSS:
    m_OverrideState: 0
    m_Value: 0.5
  m_SecondDenoiserPassSS:
    m_OverrideState: 0
    m_Value: 1
  lastBounceFallbackHierarchy:
    m_OverrideState: 1
    m_Value: 2
  ambientProbeDimmer:
    m_OverrideState: 1
    m_Value: 0
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  textureLodBias:
    m_OverrideState: 0
    m_Value: 7
  m_RayLength:
    m_OverrideState: 0
    m_Value: 50
  m_ClampValue:
    m_OverrideState: 0
    m_Value: 100
  mode:
    m_OverrideState: 0
    m_Value: 2
  m_FullResolution:
    m_OverrideState: 0
    m_Value: 0
  sampleCount:
    m_OverrideState: 0
    m_Value: 2
  bounceCount:
    m_OverrideState: 0
    m_Value: 1
  m_Denoise:
    m_OverrideState: 0
    m_Value: 1
  m_HalfResolutionDenoiser:
    m_OverrideState: 0
    m_Value: 0
  m_DenoiserRadius:
    m_OverrideState: 0
    m_Value: 0.66
  m_SecondDenoiserPass:
    m_OverrideState: 0
    m_Value: 1
  m_MaxMixedRaySteps:
    m_OverrideState: 0
    m_Value: 48
  receiverMotionRejection:
    m_OverrideState: 0
    m_Value: 1
--- !u!114 &-7421438735674079228
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: ScreenSpaceAmbientOcclusion
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 0
    m_Value: 1
  rayTracing:
    m_OverrideState: 1
    m_Value: 1
  intensity:
    m_OverrideState: 0
    m_Value: 0
  directLightingStrength:
    m_OverrideState: 0
    m_Value: 0
  radius:
    m_OverrideState: 0
    m_Value: 2
  spatialBilateralAggressiveness:
    m_OverrideState: 0
    m_Value: 0.15
  temporalAccumulation:
    m_OverrideState: 0
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 0
    m_Value: 0.5
  blurSharpness:
    m_OverrideState: 0
    m_Value: 0.1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  specularOcclusion:
    m_OverrideState: 0
    m_Value: 0.5
  occluderMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  receiverMotionRejection:
    m_OverrideState: 0
    m_Value: 1
  m_StepCount:
    m_OverrideState: 0
    m_Value: 6
  m_FullResolution:
    m_OverrideState: 0
    m_Value: 0
  m_MaximumRadiusInPixels:
    m_OverrideState: 0
    m_Value: 40
  m_BilateralUpsample:
    m_OverrideState: 0
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 0
    m_Value: 2
  m_RayLength:
    m_OverrideState: 0
    m_Value: 3
  m_SampleCount:
    m_OverrideState: 0
    m_Value: 2
  m_Denoise:
    m_OverrideState: 0
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 0
    m_Value: 0.5
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: Indoors RayTracing Profile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 696833525676062292}
  - {fileID: -9017800197243233644}
  - {fileID: -7421438735674079228}
--- !u!114 &696833525676062292
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 384c4d03a551c44448145f4093304119, type: 3}
  m_Name: ScreenSpaceReflection
  m_EditorClassIdentifier: 
  active: 1
  quality:
    m_OverrideState: 0
    m_Value: 3
  enabled:
    m_OverrideState: 1
    m_Value: 1
  enabledTransparent:
    m_OverrideState: 1
    m_Value: 1
  tracing:
    m_OverrideState: 1
    m_Value: 2
  m_MinSmoothness:
    m_OverrideState: 0
    m_Value: 0.9
  m_SmoothnessFadeStart:
    m_OverrideState: 0
    m_Value: 0.9
  reflectSky:
    m_OverrideState: 0
    m_Value: 1
  usedAlgorithm:
    m_OverrideState: 1
    m_Value: 0
  depthBufferThickness:
    m_OverrideState: 0
    m_Value: 0.01
  screenFadeDistance:
    m_OverrideState: 0
    m_Value: 0.1
  accumulationFactor:
    m_OverrideState: 0
    m_Value: 0.75
  biasFactor:
    m_OverrideState: 0
    m_Value: 0.5
  speedRejectionParam:
    m_OverrideState: 0
    m_Value: 0.5
  speedRejectionScalerFactor:
    m_OverrideState: 0
    m_Value: 0.2
  speedSmoothReject:
    m_OverrideState: 0
    m_Value: 0
  speedSurfaceOnly:
    m_OverrideState: 0
    m_Value: 1
  speedTargetOnly:
    m_OverrideState: 0
    m_Value: 1
  enableWorldSpeedRejection:
    m_OverrideState: 0
    m_Value: 0
  m_RayMaxIterations:
    m_OverrideState: 0
    m_Value: 32
  rayMiss:
    m_OverrideState: 0
    m_Value: 3
  lastBounceFallbackHierarchy:
    m_OverrideState: 0
    m_Value: 3
  ambientProbeDimmer:
    m_OverrideState: 1
    m_Value: 1
  layerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  textureLodBias:
    m_OverrideState: 0
    m_Value: 1
  m_RayLength:
    m_OverrideState: 0
    m_Value: 50
  m_ClampValue:
    m_OverrideState: 0
    m_Value: 100
  m_Denoise:
    m_OverrideState: 0
    m_Value: 1
  m_DenoiserRadius:
    m_OverrideState: 0
    m_Value: 0.75
  m_DenoiserAntiFlickeringStrength:
    m_OverrideState: 0
    m_Value: 1
  mode:
    m_OverrideState: 0
    m_Value: 2
  m_FullResolution:
    m_OverrideState: 0
    m_Value: 0
  sampleCount:
    m_OverrideState: 0
    m_Value: 1
  bounceCount:
    m_OverrideState: 0
    m_Value: 1
  m_RayMaxIterationsRT:
    m_OverrideState: 0
    m_Value: 48
  adaptiveProbeVolumesLayerMask:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 0
      m_Bits: 1
