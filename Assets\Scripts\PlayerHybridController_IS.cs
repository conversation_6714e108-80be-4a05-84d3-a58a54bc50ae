// PlayerHybridController_IS.cs
using UnityEngine;
using UnityEngine.InputSystem;

[RequireComponent(typeof(CharacterController))]
public class PlayerHybridController_IS : MonoBehaviour
{
    public enum ViewMode { TopDown, TPS }

    public bool tpsOrientToMovement = false; // false = strafe (reco); true = tourner vers le move
    public InputActionReference aim;

    [Header("Refs")]
    public CharacterController controller;

    public Transform rotateTarget;

    public Transform cameraTransform; // Main Camera
    public CameraSwitcherCinemachine camSwitcher; // pour connaître le mode courant
    public GameSettings settings;

    [Header("Move")]
    public float moveSpeed = 6f;
    public float sprintSpeed = 9f;
    public float rotationSpeed = 720f;

    [Header("Jump / Gravity")]
    public bool enableJump = true;
    public float jumpHeight = 1.35f;
    public float gravity = -20f;
    [Tooltip("Temps après avoir quitté le sol pendant lequel un saut est accepté.")]
    public float coyoteTime = 0.15f;
    [Tooltip("Temps pendant lequel un appui saut est mis en mémoire avant d’atterrir.")]
    public float jumpBuffer = 0.12f;
    [Tooltip("Rayon de détection du sol pour une grounded plus stable qu'isGrounded seul.")]
    public float groundCheckRadius = 0.2f;
    public Vector3 groundCheckOffset = new Vector3(0, -0.1f, 0);
    public LayerMask groundMask = ~0;

    [Header("Dash")]
    public bool enableDash = true;
    public float dashSpeed = 14f;
    public float dashDuration = 0.15f;
    public float dashCooldown = 0.4f;

    [Header("Input")]
    public InputActionReference move;      // Vector2
    public InputActionReference look;      // Vector2 (pour TopDown only)
    public InputActionReference jump;      // Button
    public InputActionReference dash;      // Button
    public InputActionReference sprint;    // Button

    private Vector3 velocity;
    private float coyoteTimer, jumpBufferTimer;
    private float dashTimer, dashCooldownTimer;
    private Vector3 dashDirection;
    private bool grounded;

    void Reset()
    {
        controller = GetComponent<CharacterController>();
    }

    void Awake()
    {
        if (!controller) controller = GetComponent<CharacterController>();
        if (!cameraTransform && Camera.main) cameraTransform = Camera.main.transform;
        if (!rotateTarget) rotateTarget = transform; // fallback
        settings?.Load();
    }
    void OnEnable()
    {
        move?.action.Enable();
        look?.action.Enable();
        jump?.action.Enable();
        dash?.action.Enable();
        sprint?.action.Enable();

        if (jump) jump.action.performed += OnJumpPerformed;
    }

    void OnDisable()
    {
        if (jump) jump.action.performed -= OnJumpPerformed;

        move?.action.Disable();
        look?.action.Disable();
        jump?.action.Disable();
        dash?.action.Disable();
        sprint?.action.Disable();
    }

    void OnJumpPerformed(InputAction.CallbackContext ctx)
    {
        jumpBufferTimer = jumpBuffer; // on met en mémoire l’appui
    }

    void Update()
    {
        // ----- Grounded robuste -----
        grounded = CheckGrounded();

        // timers
        if (grounded) coyoteTimer = coyoteTime; else coyoteTimer -= Time.deltaTime;
        if (jumpBufferTimer > 0f) jumpBufferTimer -= Time.deltaTime;

        // ----- Inputs -----
        Vector2 m = move ? move.action.ReadValue<Vector2>() : Vector2.zero;
        Vector3 input = new Vector3(m.x, 0, m.y);
        input = Vector3.ClampMagnitude(input, 1f);

        // ----- Déplacement selon mode -----
        var mode = camSwitcher ? (camSwitcher.mode == CameraSwitcherCinemachine.ViewMode.TopDown ? ViewMode.TopDown : ViewMode.TPS) : ViewMode.TopDown;
        Vector3 moveDir = Vector3.zero;

        if (mode == ViewMode.TopDown)
        {
            moveDir = input; // world-space (Z avant)

            // Orientation à la souris projetée au sol
            if (cameraTransform)
            {
                Ray ray = Camera.main.ScreenPointToRay(Mouse.current.position.ReadValue());
                Plane plane = new Plane(Vector3.up, new Vector3(0f, transform.position.y, 0f));
                if (plane.Raycast(ray, out float enter))
                {
                    Vector3 lookPoint = ray.GetPoint(enter);
                    Vector3 to = lookPoint - transform.position; to.y = 0f;
                    if (to.sqrMagnitude > 0.0001f)
                    {
                        Quaternion target = Quaternion.LookRotation(to, Vector3.up);
                        transform.rotation = Quaternion.Slerp(transform.rotation, target, rotationSpeed * Time.deltaTime);
                    }
                }
            }
        }
        else // TPS
        {
            Vector3 camForward = Vector3.Scale(cameraTransform.forward, new Vector3(1, 0, 1)).normalized;
            Debug.Log("camForward: " + camForward);
            if (camForward.sqrMagnitude > 0.0001f)
            {
                Quaternion target = Quaternion.LookRotation(camForward, Vector3.up);
                rotateTarget.rotation = Quaternion.RotateTowards(rotateTarget.rotation, target, rotationSpeed * Time.deltaTime);
            }

        }

        // Sprint
        float speed = (sprint != null && sprint.action.IsPressed()) ? sprintSpeed : moveSpeed;
        Vector3 horizontal = moveDir * speed;

        // ----- Dash -----
        dashCooldownTimer -= Time.deltaTime;
        if (enableDash)
        {
            if (dashTimer > 0f) dashTimer -= Time.deltaTime;
            else if (dashCooldownTimer <= 0f && dash != null && dash.action.WasPerformedThisFrame())
            {
                dashDirection = (moveDir.sqrMagnitude > 0f ? moveDir : transform.forward);
                dashTimer = dashDuration;
                dashCooldownTimer = dashCooldown;
            }
            if (dashTimer > 0f) horizontal = dashDirection * dashSpeed;
        }

        // ----- Saut (coyote + buffer) -----
        if (enableJump)
        {
            if (grounded && velocity.y < 0f) velocity.y = -2f;

            bool canJump = coyoteTimer > 0f && jumpBufferTimer > 0f;
            if (canJump)
            {
                jumpBufferTimer = 0f;
                coyoteTimer = 0f;
                velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
            }
        }

        // gravité
        velocity.y += gravity * Time.deltaTime;

        // ----- Move final -----
        controller.Move((horizontal + velocity) * Time.deltaTime);
    }

    bool CheckGrounded()
    {
        // combine CharacterController.isGrounded + spherecast court
        bool c1 = controller.isGrounded;
        Vector3 origin = transform.position + groundCheckOffset;
        bool c2 = Physics.CheckSphere(origin, groundCheckRadius, groundMask, QueryTriggerInteraction.Ignore);
        return c1 || c2;
    }

    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Vector3 origin = transform.position + groundCheckOffset;
        Gizmos.DrawWireSphere(origin, groundCheckRadius);
    }
    void LateUpdate()
    {
        // Force le yaw du perso = yaw caméra (TPS)
        Vector3 camF = Vector3.ProjectOnPlane(cameraTransform.forward, Vector3.up).normalized;
        if (camF.sqrMagnitude < 1e-4f) return;

        Quaternion target = Quaternion.LookRotation(camF, Vector3.up);
        rotateTarget.rotation = Quaternion.RotateTowards(
            rotateTarget.rotation, target, rotationSpeed * Time.deltaTime
        );
    }

}
